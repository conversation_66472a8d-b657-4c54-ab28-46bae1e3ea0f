import { <PERSON>ala<PERSON> } from '../nodes/Scalar.js';
import type { BlockScalar, FlowScalar, SourceToken } from '../parse/cst.js';
import type { ComposeContext } from './compose-node.js';
import type { ComposeErrorHandler } from './composer.js';
export declare function composeScalar(ctx: ComposeContext, token: FlowScalar | BlockScalar, tagToken: SourceToken | null, onError: ComposeErrorHandler): Scalar.Parsed;
